#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import asyncio
import time
from typing import List, Dict, Any, Optional
from dataclasses import dataclass

from .http_client import HttpClient, HttpResponse
from .qps_controller import QpsController
from config.request_config import BaseRequestBuilder, AdRequestInfo


@dataclass
class ExecutionResult:
    """执行结果封装"""
    request_type: str
    target_qps: int
    actual_qps: float
    total_requests: int
    success_requests: int
    fill_requests: int
    error_requests: int
    responses: List[HttpResponse]
    request_times: List[float]
    start_time: float
    end_time: float
    duration: float
    
    @property
    def success_rate(self) -> float:
        """成功率"""
        return (self.success_requests / self.total_requests * 100) if self.total_requests > 0 else 0
    
    @property
    def fill_rate(self) -> float:
        """填充率（状态码200的请求比例）"""
        return (self.fill_requests / self.total_requests * 100) if self.total_requests > 0 else 0
    
    @property
    def qps_accuracy(self) -> float:
        """QPS精度"""
        if self.target_qps == 0:
            return 100.0
        return 100 - abs(self.target_qps - self.actual_qps) / self.target_qps * 100


class RequestExecutor:
    """请求执行器"""
    
    def __init__(self, http_client: HttpClient, qps_controller: QpsController):
        self.http_client = http_client
        self.qps_controller = qps_controller
    
    async def execute_test(self,
                          request_builder: BaseRequestBuilder,
                          ad_request: AdRequestInfo,
                          qps: int,
                          duration: int,
                          max_requests: Optional[int] = None) -> ExecutionResult:
        """
        执行QPS测试

        Args:
            request_builder: 请求构建器
            ad_request: 广告请求信息
            qps: 目标QPS
            duration: 持续时间（秒）
            max_requests: 最大请求数限制

        Returns:
            ExecutionResult: 执行结果
        """
        print(f"开始{request_builder.get_name()}请求测试 - QPS: {qps}, 持续时间: {duration}秒")
        
        responses: List[HttpResponse] = []
        request_times: List[float] = []
        start_time = time.time()
        
        # 创建任务列表
        tasks = []
        request_count = 0
        
        while True:
            current_time = time.time()
            elapsed = current_time - start_time
            
            # 检查是否达到时间限制
            if elapsed >= duration:
                break
            
            # 检查是否达到请求数限制
            if max_requests and request_count >= max_requests:
                break
            
            # 创建请求任务
            task = asyncio.create_task(
                self._execute_single_request(request_builder, ad_request, current_time)
            )
            tasks.append(task)
            request_times.append(current_time)
            request_count += 1
            
            # 等待下一个请求时间
            if qps > 0:
                await asyncio.sleep(1.0 / qps)
        
        # 等待所有任务完成
        if tasks:
            responses = await asyncio.gather(*tasks, return_exceptions=True)
            # 过滤异常结果
            responses = [r for r in responses if isinstance(r, HttpResponse)]
        
        end_time = time.time()
        actual_duration = end_time - start_time
        actual_qps = len(responses) / actual_duration if actual_duration > 0 else 0
        
        # 统计结果
        success_requests = sum(1 for r in responses if r.success)
        fill_requests = sum(1 for r in responses if r.success and r.status_code == 200)
        error_requests = len(responses) - success_requests
        
        result = ExecutionResult(
            request_type=request_builder.get_name(),
            target_qps=qps,
            actual_qps=round(actual_qps, 2),
            total_requests=len(responses),
            success_requests=success_requests,
            fill_requests=fill_requests,
            error_requests=error_requests,
            responses=responses,
            request_times=request_times,
            start_time=start_time,
            end_time=end_time,
            duration=actual_duration
        )
        
        self._print_result_summary(result)
        return result
    
    async def _execute_single_request(self,
                                    request_builder: BaseRequestBuilder,
                                    ad_request: AdRequestInfo,
                                    request_time: float) -> HttpResponse:
        """执行单个请求"""
        async with self.qps_controller:
            payload = request_builder.build_payload(ad_request)
            headers = request_builder.get_headers()
            timeout = request_builder.get_timeout()

            response = await self.http_client.post(
                url=request_builder.get_url(),
                payload=payload,
                headers=headers,
                timeout=timeout
            )

            return response
    
    def _print_result_summary(self, result: ExecutionResult) -> None:
        """打印结果摘要"""
        print(f"\n{result.request_type}测试完成:")
        print(f"  目标QPS: {result.target_qps}")
        print(f"  实际QPS: {result.actual_qps}")
        print(f"  QPS精度: {result.qps_accuracy:.1f}%")
        print(f"  总请求数: {result.total_requests}")
        print(f"  成功请求数: {result.success_requests}")
        print(f"  填充请求数: {result.fill_requests}")
        print(f"  错误请求数: {result.error_requests}")
        print(f"  成功率: {result.success_rate:.2f}%")
        print(f"  填充率: {result.fill_rate:.2f}%")
        print(f"  持续时间: {result.duration:.2f}秒")


class BatchExecutor:
    """批量执行器"""
    
    def __init__(self, http_client: HttpClient):
        self.http_client = http_client
    
    async def execute_multiple_tests(self, 
                                   test_configs: List[Dict[str, Any]]) -> List[ExecutionResult]:
        """
        执行多个测试配置
        
        Args:
            test_configs: 测试配置列表，每个配置包含：
                - request_builder: BaseRequestBuilder
                - ad_request: AdRequestInfo
                - qps: int
                - duration: int
                - qps_controller: QpsController
        
        Returns:
            List[ExecutionResult]: 执行结果列表
        """
        results = []
        
        for test_config in test_configs:
            executor = RequestExecutor(
                self.http_client, 
                test_config['qps_controller']
            )
            
            result = await executor.execute_test(
                request_builder=test_config['request_builder'],
                ad_request=test_config['ad_request'],
                qps=test_config['qps'],
                duration=test_config['duration'],
                max_requests=test_config.get('max_requests')
            )
            
            results.append(result)
            
            # 测试间隔
            if 'interval' in test_config:
                await asyncio.sleep(test_config['interval'])
        
        return results
