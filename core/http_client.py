#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import asyncio
import aiohttp
import time
from typing import Dict, Any, Optional, Union
from abc import ABC, abstractmethod


class HttpResponse:
    """HTTP响应封装类"""
    
    def __init__(self, success: bool, status_code: int, 
                 response_text: str, elapsed_time: float):
        self.success = success
        self.status_code = status_code
        self.response_text = response_text
        self.elapsed_time = elapsed_time
        self.timestamp = time.time()
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'success': self.success,
            'code': self.status_code,
            'response_text': self.response_text,
            'elapsed_time': self.elapsed_time,
            'timestamp': self.timestamp
        }


class HttpClient(ABC):
    """HTTP客户端抽象基类"""
    
    @abstractmethod
    async def post(self, url: str, payload: Dict[str, Any], 
                   headers: Optional[Dict[str, str]] = None,
                   timeout: Optional[float] = None) -> HttpResponse:
        """发送POST请求"""
        pass
    
    @abstractmethod
    async def get(self, url: str, params: Optional[Dict[str, Any]] = None,
                  headers: Optional[Dict[str, str]] = None,
                  timeout: Optional[float] = None) -> HttpResponse:
        """发送GET请求"""
        pass
    
    @abstractmethod
    async def close(self) -> None:
        """关闭客户端"""
        pass


class AsyncHttpClient(HttpClient):
    """基于aiohttp的异步HTTP客户端"""
    
    def __init__(self, connector_limit: int = 100, 
                 connector_limit_per_host: int = 30,
                 timeout: float = 10.0):
        """
        初始化HTTP客户端
        
        Args:
            connector_limit: 总连接池大小
            connector_limit_per_host: 每个主机的连接数限制
            timeout: 默认超时时间
        """
        self.default_timeout = timeout
        self.connector = aiohttp.TCPConnector(
            limit=connector_limit,
            limit_per_host=connector_limit_per_host,
            enable_cleanup_closed=True
        )
        self.session: Optional[aiohttp.ClientSession] = None
    
    async def _ensure_session(self) -> aiohttp.ClientSession:
        """确保session已创建"""
        if self.session is None or self.session.closed:
            timeout = aiohttp.ClientTimeout(total=self.default_timeout)
            self.session = aiohttp.ClientSession(
                connector=self.connector,
                timeout=timeout
            )
        return self.session
    
    async def post(self, url: str, payload: Dict[str, Any],
                   headers: Optional[Dict[str, str]] = None,
                   timeout: Optional[float] = None) -> HttpResponse:
        """发送POST请求"""
        session = await self._ensure_session()
        start_time = time.time()
        
        try:
            request_timeout = timeout or self.default_timeout
            timeout_obj = aiohttp.ClientTimeout(total=request_timeout)
            
            default_headers = {'Content-Type': 'application/json'}
            if headers:
                default_headers.update(headers)
            
            async with session.post(
                url, 
                json=payload, 
                headers=default_headers,
                timeout=timeout_obj
            ) as response:
                response_text = await response.text()
                elapsed_time = time.time() - start_time
                
                return HttpResponse(
                    success=response.status in [200, 204],
                    status_code=response.status,
                    response_text=response_text,
                    elapsed_time=elapsed_time
                )
                
        except Exception as e:
            elapsed_time = time.time() - start_time
            return HttpResponse(
                success=False,
                status_code=0,
                response_text=str(e),
                elapsed_time=elapsed_time
            )
    
    async def get(self, url: str, params: Optional[Dict[str, Any]] = None,
                  headers: Optional[Dict[str, str]] = None,
                  timeout: Optional[float] = None) -> HttpResponse:
        """发送GET请求"""
        session = await self._ensure_session()
        start_time = time.time()
        
        try:
            request_timeout = timeout or self.default_timeout
            timeout_obj = aiohttp.ClientTimeout(total=request_timeout)
            
            async with session.get(
                url,
                params=params,
                headers=headers,
                timeout=timeout_obj
            ) as response:
                response_text = await response.text()
                elapsed_time = time.time() - start_time
                
                return HttpResponse(
                    success=response.status in [200, 204],
                    status_code=response.status,
                    response_text=response_text,
                    elapsed_time=elapsed_time
                )
                
        except Exception as e:
            elapsed_time = time.time() - start_time
            return HttpResponse(
                success=False,
                status_code=0,
                response_text=str(e),
                elapsed_time=elapsed_time
            )
    
    async def close(self) -> None:
        """关闭客户端"""
        if self.session and not self.session.closed:
            await self.session.close()
        if self.connector:
            await self.connector.close()
    
    async def __aenter__(self):
        """异步上下文管理器入口"""
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        await self.close()
