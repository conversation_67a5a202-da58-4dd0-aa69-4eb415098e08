#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import asyncio
import aiolimiter
from typing import Optional
from abc import ABC, abstractmethod


class QpsController(ABC):
    """QPS控制器抽象基类"""
    
    @abstractmethod
    async def acquire(self) -> None:
        """获取执行权限"""
        pass
    
    @abstractmethod
    async def __aenter__(self):
        """异步上下文管理器入口"""
        pass
    
    @abstractmethod
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        pass


class AsyncQpsController(QpsController):
    """基于aiolimiter的异步QPS控制器"""
    
    def __init__(self, qps: int, max_concurrent: Optional[int] = None):
        """
        初始化QPS控制器
        
        Args:
            qps: 每秒请求数
            max_concurrent: 最大并发数，默认为qps*2但不超过100
        """
        self.qps = qps
        self.limiter = aiolimiter.AsyncLimiter(max_rate=qps, time_period=1.0)
        
        # 设置合理的并发数限制
        if max_concurrent is None:
            max_concurrent = qps * 3
        self.semaphore = asyncio.Semaphore(max_concurrent)
        
    async def acquire(self) -> None:
        """获取执行权限"""
        await self.semaphore.acquire()
        await self.limiter.acquire()
    
    def release(self) -> None:
        """释放并发权限"""
        self.semaphore.release()
    
    async def __aenter__(self):
        """异步上下文管理器入口"""
        await self.acquire()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        self.release()


class TokenBucketController(QpsController):
    """基于令牌桶算法的QPS控制器"""
    
    def __init__(self, qps: int, burst_size: Optional[int] = None):
        """
        初始化令牌桶控制器
        
        Args:
            qps: 每秒请求数
            burst_size: 突发请求大小，默认等于qps
        """
        self.qps = qps
        self.burst_size = burst_size or qps
        self.tokens = self.burst_size
        self.last_refill = asyncio.get_event_loop().time()
        self.lock = asyncio.Lock()
    
    async def acquire(self) -> None:
        """获取执行权限"""
        async with self.lock:
            now = asyncio.get_event_loop().time()
            # 添加令牌
            elapsed = now - self.last_refill
            self.tokens = min(self.burst_size, self.tokens + elapsed * self.qps)
            self.last_refill = now
            
            # 如果没有令牌，等待
            if self.tokens < 1:
                wait_time = (1 - self.tokens) / self.qps
                await asyncio.sleep(wait_time)
                self.tokens = 0
            else:
                self.tokens -= 1
    
    async def __aenter__(self):
        """异步上下文管理器入口"""
        await self.acquire()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        pass
