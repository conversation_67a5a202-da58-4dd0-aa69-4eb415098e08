#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import time
from typing import List, Dict, Any, Optional
from dataclasses import dataclass, asdict
import json

from .executor import ExecutionResult


@dataclass
class QpsDistribution:
    """QPS分布统计"""
    second: int
    requests_count: int
    timestamp: float


@dataclass
class PerformanceMetrics:
    """性能指标"""
    avg_response_time: float
    min_response_time: float
    max_response_time: float
    p50_response_time: float
    p90_response_time: float
    p95_response_time: float
    p99_response_time: float


class ResultAnalyzer:
    """结果分析器"""
    
    def __init__(self):
        pass
    
    def analyze_single_result(self, result: ExecutionResult) -> Dict[str, Any]:
        """分析单个测试结果"""
        # 基础统计
        basic_stats = {
            'request_type': result.request_type,
            'target_qps': result.target_qps,
            'actual_qps': result.actual_qps,
            'qps_accuracy': result.qps_accuracy,
            'total_requests': result.total_requests,
            'success_requests': result.success_requests,
            'fill_requests': result.fill_requests,
            'error_requests': result.error_requests,
            'success_rate': result.success_rate,
            'fill_rate': result.fill_rate,
            'duration': result.duration
        }
        
        # QPS分布
        qps_distribution = self._calculate_qps_distribution(result)
        
        # 性能指标
        performance_metrics = self._calculate_performance_metrics(result)
        
        # 错误分析
        error_analysis = self._analyze_errors(result)
        
        return {
            'basic_stats': basic_stats,
            'qps_distribution': qps_distribution,
            'performance_metrics': asdict(performance_metrics),
            'error_analysis': error_analysis,
            'timestamp': time.time()
        }
    
    def _calculate_qps_distribution(self, result: ExecutionResult) -> List[Dict[str, Any]]:
        """计算QPS分布"""
        if not result.request_times:
            return []
        
        distribution = []
        start_time = result.start_time
        duration_seconds = int(result.duration) + 1
        
        for second in range(duration_seconds):
            second_start = start_time + second
            second_end = start_time + second + 1
            
            count = sum(1 for t in result.request_times 
                       if second_start <= t < second_end)
            
            distribution.append({
                'second': second,
                'requests_count': count,
                'timestamp': second_start
            })
        
        return distribution
    
    def _calculate_performance_metrics(self, result: ExecutionResult) -> PerformanceMetrics:
        """计算性能指标"""
        if not result.responses:
            return PerformanceMetrics(0, 0, 0, 0, 0, 0, 0)
        
        response_times = [r.elapsed_time for r in result.responses if r.success]
        
        if not response_times:
            return PerformanceMetrics(0, 0, 0, 0, 0, 0, 0)
        
        response_times.sort()
        count = len(response_times)
        
        return PerformanceMetrics(
            avg_response_time=sum(response_times) / count,
            min_response_time=min(response_times),
            max_response_time=max(response_times),
            p50_response_time=self._percentile(response_times, 50),
            p90_response_time=self._percentile(response_times, 90),
            p95_response_time=self._percentile(response_times, 95),
            p99_response_time=self._percentile(response_times, 99)
        )
    
    def _percentile(self, data: List[float], percentile: int) -> float:
        """计算百分位数"""
        if not data:
            return 0.0
        
        index = (percentile / 100.0) * (len(data) - 1)
        if index.is_integer():
            return data[int(index)]
        else:
            lower = data[int(index)]
            upper = data[int(index) + 1]
            return lower + (upper - lower) * (index - int(index))
    
    def _analyze_errors(self, result: ExecutionResult) -> Dict[str, Any]:
        """分析错误"""
        error_stats = {}
        error_details = []
        
        for response in result.responses:
            if not response.success:
                error_key = f"status_{response.status_code}"
                error_stats[error_key] = error_stats.get(error_key, 0) + 1
                
                error_details.append({
                    'status_code': response.status_code,
                    'error_message': response.response_text[:200],  # 截取前200字符
                    'timestamp': response.timestamp,
                    'elapsed_time': response.elapsed_time
                })
        
        return {
            'error_stats': error_stats,
            'error_details': error_details[:10]  # 只保留前10个错误详情
        }
    
    def compare_results(self, results: List[ExecutionResult]) -> Dict[str, Any]:
        """比较多个测试结果"""
        if not results:
            return {}
        
        comparison = {
            'summary': {
                'total_tests': len(results),
                'test_types': list(set(r.request_type for r in results)),
                'total_requests': sum(r.total_requests for r in results),
                'total_duration': sum(r.duration for r in results)
            },
            'qps_comparison': [],
            'performance_comparison': [],
            'success_rate_comparison': []
        }
        
        for result in results:
            comparison['qps_comparison'].append({
                'request_type': result.request_type,
                'target_qps': result.target_qps,
                'actual_qps': result.actual_qps,
                'qps_accuracy': result.qps_accuracy
            })
            
            metrics = self._calculate_performance_metrics(result)
            comparison['performance_comparison'].append({
                'request_type': result.request_type,
                'avg_response_time': metrics.avg_response_time,
                'p95_response_time': metrics.p95_response_time
            })
            
            comparison['success_rate_comparison'].append({
                'request_type': result.request_type,
                'success_rate': result.success_rate,
                'fill_rate': result.fill_rate
            })
        
        return comparison
    
    def export_results(self, results: List[ExecutionResult], 
                      filename: Optional[str] = None) -> str:
        """导出结果到JSON文件"""
        if filename is None:
            timestamp = int(time.time())
            filename = f"qps_test_results_{timestamp}.json"
        
        export_data = {
            'export_time': time.time(),
            'results': []
        }
        
        for result in results:
            analyzed_result = self.analyze_single_result(result)
            export_data['results'].append(analyzed_result)
        
        # 添加比较分析
        if len(results) > 1:
            export_data['comparison'] = self.compare_results(results)
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(export_data, f, indent=2, ensure_ascii=False)
        
        print(f"结果已导出到: {filename}")
        return filename
