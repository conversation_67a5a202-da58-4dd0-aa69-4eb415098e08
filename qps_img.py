import requests
import json
import time
import threading
from datetime import datetime, timedelta
from collections import defaultdict, deque

import matplotlib.pyplot as plt
import matplotlib.dates as mdates
from matplotlib.animation import FuncAnimation

# --- 配置区 ---
BASE_URL = "http://172.31.236.166:8089/dataTest"  # 请替换为你的服务地址
SSP_QPS_URL = f"{BASE_URL}/sspQps"
DSP_QPS_URL = f"{BASE_URL}/dspQps"
DSP_BID_URL = f"{BASE_URL}/dspBid"
CTRL_QPS_URL = f"{BASE_URL}/ctrlQps"

SAMPLE_INTERVAL = 2  # 数据采集频率（秒）
MAX_TIME_WINDOW_SECONDS = 600  # X轴最大显示时间窗口（秒）
# 评估：120秒窗口，每2秒一个点，最多60个点，全屏显示非常清晰。
# 如果需要更长周期，比如1000秒（约16分钟），下面的降采样逻辑会自动生效。

# --- 数据采集模块 (与原版基本一致，但使用deque以提高性能) ---
class DataCollector(threading.Thread):
    def __init__(self):
        super().__init__(daemon=True)
        self.data_lock = threading.Lock()
        
        # 使用deque可以高效地在两端添加和删除元素
        self.ssp_qps_data = defaultdict(lambda: deque(maxlen=int(MAX_TIME_WINDOW_SECONDS / SAMPLE_INTERVAL * 1.5)))
        self.dsp_qps_data = defaultdict(lambda: deque(maxlen=int(MAX_TIME_WINDOW_SECONDS / SAMPLE_INTERVAL * 1.5)))
        self.dsp_bid_data = defaultdict(lambda: deque(maxlen=int(MAX_TIME_WINDOW_SECONDS / SAMPLE_INTERVAL * 1.5)))
        self.ctrl_qps_data = defaultdict(lambda: deque(maxlen=int(MAX_TIME_WINDOW_SECONDS / SAMPLE_INTERVAL * 1.5)))
        self.timestamps = deque(maxlen=int(MAX_TIME_WINDOW_SECONDS / SAMPLE_INTERVAL * 1.5))

    def run(self):
        while True:
            try:
                ssp_res = requests.get(SSP_QPS_URL, timeout=1).json()
                dsp_res = requests.get(DSP_QPS_URL, timeout=1).json()
                bid_res = requests.get(DSP_BID_URL, timeout=1).json()
                ctrl_res = requests.get(CTRL_QPS_URL, timeout=1).json()

                current_time = datetime.now()

                with self.data_lock:
                    self.timestamps.append(current_time)
                    self._update_data_series(self.ssp_qps_data, ssp_res)
                    self._update_data_series(self.dsp_qps_data, dsp_res)
                    self._update_data_series(self.dsp_bid_data, bid_res)
                    self._update_data_series(self.ctrl_qps_data, ctrl_res)

            except requests.exceptions.RequestException as e:
                print(f"[{datetime.now()}] Error fetching data: {e}")
            except json.JSONDecodeError as e:
                print(f"[{datetime.now()}] Error decoding JSON: {e}")

            time.sleep(SAMPLE_INTERVAL)

    def _update_data_series(self, data_dict, new_data):
        # 为所有在new_data中但不在data_dict中的key填充历史数据为0，以保持对齐
        active_keys = set(new_data.keys())
        stored_keys = set(data_dict.keys())
        for key in stored_keys - active_keys:
            data_dict[key].append(0)
        
        for key, value in new_data.items():
            data_dict[key].append(value)
            # 如果是新key，用0填充历史数据
            if len(data_dict[key]) < len(self.timestamps):
                 # Prepend with zeros to align with timestamps
                padding = [0] * (len(self.timestamps) - len(data_dict[key]))
                data_dict[key].extendleft(padding)


# --- 绘图与交互模块 ---
class RealtimePlotter:
    def __init__(self, data_collector):
        self.collector = data_collector
        self.fig, self.axs = plt.subplots(4, 1, figsize=(18, 10), sharex=True) # 调整尺寸以适应全屏
        self.lines = [{}, {}, {}, {}]
        self.annotation = None  # 全局只保留一个标注对象
        self._setup_plot()

    def _setup_plot(self):
        """设置4个子图的标题、标签和时间轴格式"""
        titles = ['Supply-Side QPS (sspQps)', 'DSP Request QPS (dspQps)', 'DSP Bidding QPS (dspBid)', 'Final Control QPS (ctrlQps)']
        y_labels = ['Available Supply', 'Actual Consumption', 'Effective Bids', 'Adjusted Limit']
        
        for i, ax in enumerate(self.axs):
            ax.set_title(titles[i], fontsize=12)
            ax.set_ylabel(y_labels[i], fontsize=10)
            ax.grid(True, linestyle='--', alpha=0.6)
            
            # **核心优化：自定义时间轴格式化器**
            # 当时间跨度大时，自动调整刻度间隔
            ax.xaxis.set_major_formatter(mdates.DateFormatter('%H:%M:%S'))
            ax.xaxis.set_major_locator(mdates.SecondLocator(interval=10)) # 主要刻度间隔10秒
            ax.xaxis.set_minor_locator(mdates.SecondLocator(interval=2))  # 次要刻度间隔2秒
            
        self.fig.suptitle('Real-time QPS Monitor (Hover to see values)', fontsize=16)
        plt.tight_layout(rect=[0, 0, 1, 0.96])
        
        # **核心优化：添加鼠标悬停事件**
        self.fig.canvas.mpl_connect('motion_notify_event', self.on_hover)

    def on_hover(self, event):
        """鼠标悬停事件处理函数"""
        if event.inaxes is None:
            return
            
        # 移除旧的标注
        if self.annotation:
            self.annotation.remove()
            self.annotation = None
        
        ax_index = list(self.axs).index(event.inaxes)
        visible_lines = [line for line in self.lines[ax_index].values() if line.get_visible()]

        for line in visible_lines:
            contains, ind = line.contains(event)
            if contains:
                # 获取鼠标悬停点的数据
                idx = ind['ind'][0]
                x_data, y_data = line.get_data()
                x, y = x_data[idx], y_data[idx]
                
                # 创建新的标注
                label = f"{line.get_label()}\nTime: {mdates.num2date(x).strftime('%H:%M:%S')}\nQPS: {y:.2f}"
                self.annotation = event.inaxes.annotate(
                    label,
                    xy=(x, y),
                    xytext=(15, 15),
                    textcoords="offset points",
                    bbox=dict(boxstyle="round,pad=0.5", fc="yellow", alpha=0.7),
                    arrowprops=dict(arrowstyle="->", connectionstyle="arc3,rad=0.1")
                )
                self.fig.canvas.draw_idle()
                return

    def update_plot(self, frame):
        """FuncAnimation的回调函数，用于更新图表"""
        with self.collector.data_lock:
            if not self.collector.timestamps:
                return

            timestamps = list(self.collector.timestamps)
            
            self._update_subplot(self.axs[0], self.lines[0], timestamps, self.collector.ssp_qps_data)
            self._update_subplot(self.axs[1], self.lines[1], timestamps, self.collector.dsp_qps_data)
            self._update_subplot(self.axs[2], self.lines[2], timestamps, self.collector.dsp_bid_data)
            self._update_subplot(self.axs[3], self.lines[3], timestamps, self.collector.ctrl_qps_data)

        # 动态调整X轴范围
        now = datetime.now()
        self.axs[0].set_xlim(now - timedelta(seconds=MAX_TIME_WINDOW_SECONDS), now + timedelta(seconds=5))

        for ax in self.axs:
            ax.relim()
            ax.autoscale_view(scalex=False) # X轴由我们手动控制
            if not ax.get_legend():
                ax.legend(loc='upper left', fontsize='small')
        
        # 旋转X轴标签以防重叠
        self.fig.autofmt_xdate()
        
    def _update_subplot(self, ax, line_dict, timestamps, data_dict):
        """更新单个子图"""
        active_keys = set(data_dict.keys())
        
        # 隐藏不再活跃的线条
        for key, line in line_dict.items():
            if key not in active_keys:
                line.set_visible(False)
        
        for key, values in data_dict.items():
            if key not in line_dict:
                line, = ax.plot(timestamps, list(values), linestyle='-', label=key, lw=2)
                line_dict[key] = line
            else:
                line = line_dict[key]
                line.set_data(timestamps, list(values))
                line.set_visible(True)

    def start(self):
        """启动动画"""
        ani = FuncAnimation(self.fig, self.update_plot, interval=SAMPLE_INTERVAL * 1000, cache_frame_data=False)
        plt.show()


if __name__ == '__main__':
    collector = DataCollector()
    collector.start()
    
    # 等待初始数据加载
    print("Waiting for initial data collection...")
    time.sleep(SAMPLE_INTERVAL * 2)
    print("Starting plot...")
    
    plotter = RealtimePlotter(collector)
    plotter.start()