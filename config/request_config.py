#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from typing import Dict, Any, Optional
from abc import ABC, abstractmethod
from dataclasses import dataclass, field


@dataclass
class MediaInfo:
    """媒体信息"""
    name: str                           # 媒体名称
    endpoint_url: str                   # 请求端点URL
    timeout: float = 10.0              # 超时时间
    headers: Dict[str, str] = field(default_factory=lambda: {'Content-Type': 'application/json'})


@dataclass
class AdRequestInfo:
    """广告请求信息"""
    ad_type: str                        # 广告类型：banner, native
    width: Optional[int] = None         # 广告宽度
    height: Optional[int] = None        # 广告高度
    app_bundle: Optional[str] = None    # App Bundle ID
    device_ip: Optional[str] = None     # 设备IP
    custom_params: Dict[str, Any] = field(default_factory=dict)  # 自定义参数


class BaseRequestBuilder(ABC):
    """请求构建器基类"""

    def __init__(self, media_info: MediaInfo):
        self.media_info = media_info

    @abstractmethod
    def build_payload(self, ad_request: AdRequestInfo) -> Dict[str, Any]:
        """构建请求payload - 子类必须实现"""
        pass

    def get_url(self) -> str:
        """获取请求URL"""
        return self.media_info.endpoint_url

    def get_headers(self) -> Dict[str, str]:
        """获取请求头"""
        return self.media_info.headers.copy()

    def get_timeout(self) -> float:
        """获取超时时间"""
        return self.media_info.timeout

    def get_name(self) -> str:
        """获取配置名称"""
        return f"{self.media_info.name}_{getattr(self, 'ad_type', 'unknown')}"


# 这里您可以实现自己的请求构建器
