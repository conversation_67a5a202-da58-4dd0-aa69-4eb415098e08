#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import json
import requests
import uuid
import time
import threading
from typing import Dict, Any
from concurrent.futures import ThreadPoolExecutor, as_completed


class PreciseQPSController:
    """精确QPS控制器"""
    
    def __init__(self, qps: int):
        self.qps = qps
        self.interval = 1.0 / qps if qps > 0 else 1.0
        self.last_request_time = 0
        self.lock = threading.Lock()
    
    def wait_for_next_request(self):
        """等待到下一个请求的时间"""
        with self.lock:
            current_time = time.time()
            if self.last_request_time == 0:
                self.last_request_time = current_time
                return
            
            expected_next_time = self.last_request_time + self.interval
            if current_time < expected_next_time:
                sleep_time = expected_next_time - current_time
                time.sleep(sleep_time)
            
            self.last_request_time = time.time()


def _send_banner_request(width: int, height: int, app_bundle: str, device_ip: str) -> Dict[str, Any]:
    """发送单个Banner请求"""
    try:
        with open('transsion-banner-app-country.json', 'r', encoding='utf-8') as f:
            payload = json.load(f)
        
        payload['id'] = str(uuid.uuid4())
        payload['imp'][0]['banner']['w'] = width
        payload['imp'][0]['banner']['h'] = height
        payload['app']['bundle'] = app_bundle
        payload['device']['ip'] = device_ip
        
        response = requests.post(
            "http://**************:8089/engine/bid/transsion",
            json=payload,
            headers={'Content-Type': 'application/json'},
            timeout=10
        )
        
        return {
            'success': response.status_code in [200, 204],
            'code': response.status_code,
            'response_text': response.text
        }
        
    except Exception as e:
        return {
            'success': False,
            'code': 0,
            'response_text': str(e)
        }


def _send_native_request(width: int, height: int, app_bundle: str, device_ip: str) -> Dict[str, Any]:
    """发送单个Native请求"""
    try:
        with open('transsion-native-app-country.json', 'r', encoding='utf-8') as f:
            payload = json.load(f)
        
        payload['id'] = str(uuid.uuid4())
        payload['app']['bundle'] = app_bundle
        payload['device']['ip'] = device_ip
        
        # 修改native request中的图片尺寸 (type=3)
        request_str = payload['imp'][0]['native']['request']
        request_obj = json.loads(request_str)
        
        for asset in request_obj['native']['assets']:
            if 'img' in asset and asset['img'].get('type') == 3:
                asset['img']['wmin'] = width
                asset['img']['hmin'] = height
                break
        
        payload['imp'][0]['native']['request'] = json.dumps(request_obj)
        
        response = requests.post(
            "http://**************:8089/engine/bid/transsion",
            json=payload,
            headers={'Content-Type': 'application/json'},
            timeout=10
        )
        
        return {
            'success': response.status_code in [200, 204],
            'code': response.status_code,
            'response_text': response.text
        }
        
    except Exception as e:
        return {
            'success': False,
            'code': 0,
            'response_text': str(e)
        }


def test_banner(qps: int, duration: int, width: int = 690, height: int = 1035,
               app_bundle: str = "com.transsion.XOSLauncher2", 
               device_ip: str = "***************") -> Dict[str, Any]:
    """
    测试Banner请求
    
    Args:
        qps: 每秒请求数
        duration: 持续时间(秒)
        width: 广告宽度，默认690
        height: 广告高度，默认1035
        app_bundle: App Bundle ID，默认com.transsion.XOSLauncher2
        device_ip: 设备IP，默认***************
    
    Returns:
        Dict包含统计信息：total_requests, success_requests, fill_requests, fill_rate等
    """
    controller = PreciseQPSController(qps)
    results = []
    request_times = []
    start_time = time.time()
    
    print(f"开始Banner请求测试 - QPS: {qps}, 持续时间: {duration}秒")
    
    with ThreadPoolExecutor(max_workers=min(qps + 10, 50)) as executor:
        futures = []
        
        while time.time() - start_time < duration:
            controller.wait_for_next_request()
            request_times.append(time.time())
            
            future = executor.submit(_send_banner_request, width, height, app_bundle, device_ip)
            futures.append(future)
        
        for future in as_completed(futures):
            results.append(future.result())
    
    return _analyze_results(results, request_times, start_time, qps, "Banner")


def test_native(qps: int, duration: int, width: int = 451, height: int = 270,
               app_bundle: str = "com.transsion.XOSLauncher",
               device_ip: str = "************") -> Dict[str, Any]:
    """
    测试Native请求
    
    Args:
        qps: 每秒请求数
        duration: 持续时间(秒)
        width: 广告图片最小宽度，默认451 (对应native request中type=3的wmin)
        height: 广告图片最小高度，默认270 (对应native request中type=3的hmin)
        app_bundle: App Bundle ID，默认com.transsion.XOSLauncher
        device_ip: 设备IP，默认************
    
    Returns:
        Dict包含统计信息：total_requests, success_requests, fill_requests, fill_rate等
    """
    controller = PreciseQPSController(qps)
    results = []
    request_times = []
    start_time = time.time()
    
    print(f"开始Native请求测试 - QPS: {qps}, 持续时间: {duration}秒")
    
    with ThreadPoolExecutor(max_workers=min(qps + 10, 50)) as executor:
        futures = []
        
        while time.time() - start_time < duration:
            controller.wait_for_next_request()
            request_times.append(time.time())
            
            future = executor.submit(_send_native_request, width, height, app_bundle, device_ip)
            futures.append(future)
        
        for future in as_completed(futures):
            results.append(future.result())
    
    return _analyze_results(results, request_times, start_time, qps, "Native")


def _analyze_results(results: list, request_times: list, start_time: float, 
                    target_qps: int, request_type: str) -> Dict[str, Any]:
    """分析测试结果"""
    total_requests = len(results)
    success_requests = sum(1 for r in results if r['success'])
    fill_requests = sum(1 for r in results if r['success'] and r['code'] == 200)
    error_requests = total_requests - success_requests
    
    actual_duration = time.time() - start_time
    actual_qps = total_requests / actual_duration if actual_duration > 0 else 0
    
    # 计算QPS分布
    qps_distribution = []
    if request_times:
        for i in range(int(actual_duration) + 1):
            second_start = start_time + i
            second_end = start_time + i + 1
            count = sum(1 for t in request_times if second_start <= t < second_end)
            qps_distribution.append(count)
    
    stats = {
        'request_type': request_type,
        'target_qps': target_qps,
        'actual_qps': round(actual_qps, 2),
        'qps_accuracy': round(100 - abs(target_qps - actual_qps) / target_qps * 100, 1),
        'total_requests': total_requests,
        'success_requests': success_requests,
        'fill_requests': fill_requests,
        'error_requests': error_requests,
        'fill_rate': round(fill_requests / total_requests * 100, 2) if total_requests > 0 else 0,
        'success_rate': round(success_requests / total_requests * 100, 2) if total_requests > 0 else 0,
        'actual_duration': round(actual_duration, 2),
        'qps_distribution': qps_distribution
    }
    
    print(f"{request_type}测试完成:")
    print(f"  目标QPS: {stats['target_qps']}")
    print(f"  实际QPS: {stats['actual_qps']}")
    print(f"  QPS精度: {stats['qps_accuracy']}%")
    print(f"  总请求数: {stats['total_requests']}")
    print(f"  成功请求数: {stats['success_requests']}")
    print(f"  填充请求数: {stats['fill_requests']}")
    print(f"  错误请求数: {stats['error_requests']}")
    print(f"  成功率: {stats['success_rate']}%")
    print(f"  填充率: {stats['fill_rate']}%")
    print(f"  持续时间: {stats['actual_duration']}秒")
    print(f"  每秒分布: {stats['qps_distribution']}")

    # 打印完整的result字典
    print(f"\n完整结果字典:")
    for key, value in stats.items():
        print(f"  '{key}': {value}")
    
    return stats


if __name__ == "__main__":
    # 您可以在这里调整最终的请求内容
    
    # Banner测试示例
    banner_result = test_banner(
        qps=20,
        duration=10,
        width=690,
        height=1035,
        app_bundle="com.transsion.XOSLauncher2",
        device_ip="***************"
    )
    
    # print()
    
    # Native测试示例
    native_result = test_native(
        qps=15,
        duration=10,
        width=451,
        height=270,
        app_bundle="com.transsion.XOSLauncher",
        device_ip="************"
    )
